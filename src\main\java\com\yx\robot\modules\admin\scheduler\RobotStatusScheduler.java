package com.yx.robot.modules.admin.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.constant.ControlStatusConstants;
import com.yx.robot.common.constant.RobotBaseInfoConstant;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.*;
import com.yx.robot.common.utils.IpInfoUtil;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dto.DirectionalControlDto;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.message._BatteryHealthState;
import com.yx.robot.modules.admin.message._BatteryState;
import com.yx.robot.modules.admin.message._Bool;
import com.yx.robot.modules.admin.message._MotorData;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.RobotInfoVo;
import com.yx.robot.modules.base.utils.SendMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisConnectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.yx.robot.common.constant.ControlStatusConstants.*;
import static com.yx.robot.common.constant.RabbitMqConstants.QUEUE_ROBOT_INFO_VO;
import static com.yx.robot.common.constant.RabbitMqConstants.ROUTING_KEY_ROBOT_INFO;
import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.StatusConstants.*;
import static com.yx.robot.common.constant.TopicConstants.*;
import static com.yx.robot.common.enums.AutoDockType.AUTO_DOCK;
import static com.yx.robot.common.enums.AutoDockType.HANDLE_PUSH_DOCK;
import static com.yx.robot.common.enums.SceneType.MOCK_LOCK_WARN;
import static com.yx.robot.common.enums.SceneType.SPRAY_OVER_WATER_WARNING;

/**
 * 机器人状态检测（按照优先级顺序排列，急停开关检测，定位检测，防撞条检测，障碍物检测）
 */
@Component
@Slf4j
@Order(2)
public class RobotStatusScheduler implements CommandLineRunner {

    private static final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(
            5, new BasicThreadFactory.Builder().namingPattern("scheduled-RobotStatus-%d").daemon(true).build());
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private IRobotTaskService iRobotTaskService;

    @Autowired
    private IRobotSwitchRecordService iRobotSwitchRecordService;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @Autowired
    private IRobotMotorService iRobotMotorService;

    @Autowired
    private RabbitMQScheduler rabbitMQScheduler;

    @Autowired
    private SendMessageUtil sendMessageUtil;

    @Autowired
    private IRobotDefineService iRobotDefineService;

    @Autowired
    private IRobotChargingService iRobotChargingService;

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    private static int count = 0;

    private static int count1 = 0;

    /**
     * 防撞条触发判断
     */
    private static boolean flag1 = false;

    /**
     * 活物检测触发判断
     */
    private static boolean flag3 = true;

    /**
     * 系统自检判断
     */
    private static boolean flag5 = true;

    /**
     * 充电桩检测判断
     */
    private static boolean flag6 = true;

    /**
     * 缺液警告
     */
    private static boolean flag7 = true;

    /**
     * 活物紫外检测判断
     */
    public static boolean checkUltraviolet = false;

    /**
     * 活物喷雾检测判断
     */
    public static boolean checkSpray = false;

    /**
     * 活物脉冲灯检测判断
     */
    public static boolean checkPulse = false;

    /**
     * 活物升降杆检测判断
     */
    public static boolean checkShielding = false;

    /**
     * 急停开关检测判断
     */
    private static boolean operation2 = true;

    /**
     * 消毒按键检测判断
     */
    private static boolean operation3 = true;

    /**
     * 系统启动成功，默认失败
     */
    private static boolean systemStartSuccess = false;

    /**
     * 系统自检消息发送
     */
    public static boolean systemCheckMessage = true;

    /**
     * 缺液消息发送
     */
    public static boolean sprayLiquidMessage = true;

    /**
     * 防撞条触发
     */
    public static boolean collisionMessage = true;

    /**
     * 电机检测
     */
    public static boolean motorCheckMessage = true;

    /**
     * 液位灯管话题发布控制
     * 防止有液状态持续发送
     */
    public static boolean liquidTopicCtrl = true;

    /**
     * 记录上次发送的液位灯状态，用于避免重复发送相同状态
     * -1: 未初始化, 0: 缺液, 1: 有液, 2: 满液
     */
    public static int lastLiquidLightState = -1;

    /**
     * 充电中点阵表情发布控制变量
     */
    public static boolean charingCtrl = true;

    /**
     * 满电点阵表情发布控制变量
     */
    public static boolean fullPowerCtrl = true;

    /**
     * 防跌落触发短信发送控制条件
     */
    public static Boolean fallCtrl = true;


    @Override
    public void run(String... args) {
        executorService.scheduleWithFixedDelay(() -> {
            handleRobotStatus();
            // 防止高液位语音先与系统成功启动播报
            if (systemStartSuccess) {
                handleOverSprayLiquidWarning();
            }
//            if (LocalDateTime.now().getSecond() % 60 == 0) {
//                log.info("handleRobotStatus is running");
//            }
        }, 0, 100, TimeUnit.MILLISECONDS);
        executorService.scheduleWithFixedDelay(() -> {
            saveMileage();
            countSpeak();
            if (LocalDateTime.now().getSecond() % 50 == 0) {
//                log.info("saveMileage is running");
            }
        }, 0, 1, TimeUnit.SECONDS);
        executorService.scheduleAtFixedRate(() -> {
            boolean isNetConnect = IpInfoUtil.extraNetState("www.baidu.com");
            RedisUtil.hset(ROBOT_SYS_INFO, NETWORK_STATUS, isNetConnect + "");
            log.info("网络连接状态检测：");
            if (LocalDateTime.now().getSecond() % 50 == 0) {
//                log.info("isNetConnect is running");
            }
        }, 0, 1, TimeUnit.HOURS);
        executorService.scheduleWithFixedDelay(() -> {
            try {
                //发送消息
                messageForRobotInfoProducer();
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (LocalDateTime.now().getSecond() % 50 == 0) {
//                log.info("messageForRobotInfoProducer is running");
            }
        }, 0, 1, TimeUnit.SECONDS);
        executorService.scheduleWithFixedDelay(() -> {
            try {
                handlerPoseLost();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }, 0, 1, TimeUnit.SECONDS);
        log.info("机器人状态消息推送.........");
        log.info("机器人状态检测中......");
    }

    /**
     * 在充电桩上定位丢失处理
     * 定位丢失后，重新加载地图
     */
    public void handlerPoseLost() {
        String pose = iRobotPositionService.getRobotPoseJson();
        String stamped = iRobotPositionService.getRobotPoseStampedJson();
        String changeMapStatus = RedisUtil.getValue(ROBOT_CHANGE_MAP);

        if (StringUtils.isBlank(pose) && StringUtils.isBlank(stamped) && StringUtils.isBlank(changeMapStatus)) {
            Short status = iRobotStatusService.getDockState();
            if (AUTO_DOCK.getType().equals(Integer.parseInt(status + ""))
                    || HANDLE_PUSH_DOCK.getType().equals(Integer.parseInt(status + ""))) {
                return;
            }
            log.info("定位程序异常，重启机器人");
            sendMessageUtil.sendShortMessage("定位程序异常");
            rosWebService.sendVoicePrompt(SceneType.DONT_WORK_OF_LOSE_LOCATION, null);
            String currentMapId = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
            rosWebService.selectMap(currentMapId);
        }
    }

    /**
     * 机器人状态信息发送
     */
    public void messageForRobotInfoProducer() {
        try {

            rabbitTemplate.setRoutingKey(ROUTING_KEY_ROBOT_INFO);
            rabbitTemplate.setQueue(QUEUE_ROBOT_INFO_VO);
            RobotInfoVo robotInfoVo = rosWebService.getRobotInfo();
            ArrayList<Object> arrayList = new ArrayList<>();
            arrayList.add(robotInfoVo.getDeviceTypeCode());
            arrayList.add(robotInfoVo.getStatus());
            arrayList.add(robotInfoVo.getBattery());
            arrayList.add(robotInfoVo.getIsSprayWarning());
            arrayList.add(robotInfoVo.getIsStop());
            arrayList.add(robotInfoVo.getIsCollision());
            arrayList.add(robotInfoVo.getIsIdle());
            arrayList.add(robotInfoVo.getHasMap());
            arrayList.add(robotInfoVo.getIsMapping());
            arrayList.add(robotInfoVo.getIsCharging());
            arrayList.add(robotInfoVo.getWorkingOperationStatus());
            arrayList.add(robotInfoVo.getLidarStatue());
            arrayList.add(robotInfoVo.getDiskInformation());
            arrayList.add(robotInfoVo.getBatteryPercent());
            arrayList.add(robotInfoVo.getPulseSurplusHours());
            arrayList.add(robotInfoVo.getCollisionState());
            arrayList.add(robotInfoVo.getSleepState());
            String robotInfoString = CollectionUtil.join(arrayList, ",");
            String isNetwork = RedisUtil.getHash(ROBOT_SYS_INFO, NETWORK_STATUS);
            // 网络正常则发送mq消息
            if (Boolean.TRUE.toString().equals(isNetwork)) {
                //将数组转化成字符串
                org.springframework.amqp.core.Message message = new Message(robotInfoString.getBytes(), new MessageProperties());
                rabbitTemplate.send(message);
            } else {
                RedisUtil.zadd(MESSAGE_INFO + ":status:" + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN),
                        JSON.toJSONString(robotInfoString));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 检测语音对话控制条件---待测试；
     */
    public void countSpeak() {
        Boolean hasKey = RedisUtil.judgeHasKey(TALK_STATUS_CTRL);
        if (hasKey) {
            ControlStatusConstants.TALK_STATUS = true;
        } else {
            ControlStatusConstants.TALK_STATUS = false;
        }

    }

    /**
     * 保存里程信息，单位:米
     */
    public void saveMileage() {
        DirectionalControlDto directionalControlDto = iRobotDefineService.getDirectionalControl();
        float currentSpeed = 0.0f;
        if (ObjectUtil.isNotNull(directionalControlDto)) {
            currentSpeed = directionalControlDto.getX();
        }
        String total = RedisUtil.getHash(ROBOT_SYS_INFO, MILEAGE);
        if (StringUtils.isNotBlank(total)) {
            float totalMileage = Float.parseFloat(total) + Math.abs(currentSpeed);
            RedisUtil.hset(ROBOT_SYS_INFO, MILEAGE, totalMileage + "");
        } else {
            RedisUtil.hset(ROBOT_SYS_INFO, MILEAGE, Math.abs(currentSpeed) + "");
        }
    }

    private void handleRobotStatus() {
        try {
            // 系统状态检测
            handleSystemCheck();
            // 电机状态检测
            motorCheck();
            // 防跌落检测
            motorLockCheck();
            if (systemStartSuccess) {
                // 喷雾缺液告警
                handleSprayLiquidWarning();
                // 消毒按键检测
                handleDoctorKeyCheck();
                // 防撞条检测
                handleCollisionCheck();
                // 充电或满电状态检测---用于点阵表情
                batteryCheck();
                // 低电量检测---用于点阵表情
                lowBattery();
                // 空闲状态检测---用于点阵表情
                isIdle();
                // 液位灯光检测
                handleLiquidLightCheck();
                // 电池健康状态检测
                batteryHealthCheck();
                // 急停开关检测---包含点阵表情
                handleEmergencyCheck();
                // 定位检测---包含点阵表情
                handlePositionCheck();
                // 充电桩检测
                handleDockCheck();
                // 语音警告检测
                handleVoiceWarnCheck();
            }
        } catch (Exception e) {
            e.printStackTrace();
            RedisConnectionUtils.unbindConnection(Objects.requireNonNull(redisTemplate.getConnectionFactory()));
        }
    }


    /**
     * 充电桩检测
     */
    private void handleDockCheck() throws Exception {
        Short autoDockState = iRobotStatusService.getDockState();
        // 线充灯光不为绿色BUG解决
        // 没有充电
        Short noDock = 0;
        // 空闲状态
        if(autoDockState.equals(noDock)){
            if(rosWebService.isIdle() && !CREATE_MAP_STATUE){
                rosWebService.sendRingLightPrompt(RingLightDefine.IDLE_STATE);
            }
        }else {
            _BatteryState batteryState = iRobotStatusService.getBatteryState();
            if (ObjectUtil.isNotNull(batteryState)) {
                Float remainBattery = batteryState.percentage;
                if (remainBattery <= 100f && remainBattery >= 97f) {
                    rosWebService.sendRingLightPrompt(RingLightDefine.FULL_BATTERY_STATE);
                } else {
                    rosWebService.sendRingLightPrompt(RingLightDefine.CHARGING_STATE);
                }
            }
        }
        if (!flag5 && flag6) {
            boolean res = rosWebService.isCharging();
            // 如果检测到充电桩，将短信通知条件重置
            if (!res && !iRobotStatusService.getPositionStatus()) {
                rosWebService.sendVoicePrompt(SceneType.NOT_FOUND_CHARGING, null);
                log.warn("充电桩检测失败，设置定位丢失");
                // 休眠10s
                Thread.sleep(10000);
            }
            if (ObjectUtil.isNull(autoDockState)) {
                log.warn("无法获取充电点状态");
                return;
            }
            if (autoDockState == AUTO_DOCK.getType().shortValue()
                    || autoDockState == AutoDockType.HANDLE_PUSH_DOCK.getType().shortValue()) {
                // 检测到充电桩则进行重定位
                if (flag6) {
                    log.info("首次开机，手推充电和自动充电情况下");
                    //检测到自动充电桩
                    rosWebService.sendVoicePrompt(SceneType.POSITION_SUCCESS, null);
                    //发送充电中的点阵表情
                    rosWebService.publishUtil(ExpressionType.CHARGING_ING.getValue());
                    RedisUtil.hset(ROBOT_SYS_INFO, POSITION_STATUS, SUCCESS);
                    //地图检测
                    mapCheck();
                    flag6 = false;
                }
            }
            Thread.sleep(2000);
        }
    }

    /**
     * 缺液告警
     */
    private void handleSprayLiquidWarning() throws Exception {
        boolean sprayLiquidWarning = iRobotStatusService.isSprayLiquidLevelWarning();
        // 判断是否已经执行了充电逻辑
        Object autoChargingStatusObj = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
        Integer autChargingStatus = Integer.valueOf(Objects.requireNonNull(autoChargingStatusObj).toString());
        // 缺液状态:只会在开始任务前播报，开机时也要播报一次；---杨子洋
        String currentId = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
        // 缺液状态
        if (!iRobotStatusService.isDock() && sprayLiquidWarning
                && !autChargingStatus.equals(AutoChargingStatus.DOING.getValue())
                && !autChargingStatus.equals(AutoChargingStatus.SUCCESS.getValue())
                // 如果当前任务不是返回充电类型会进入缺液状态
                && !TaskType.CHARGING.getType().equals(iRobotTaskService.getCurrentTaskType())
                && iRobotDisinfectService.isSprayDisinfect(currentId)
        ) {
            if (flag7) {
                // 先停止任务
                rosWebService.taskControl1(null, "2");
                // 再取消任务
                rosWebService.taskControl1(null, "4");
                // 语音提示
                rosWebService.sendVoicePrompt(SceneType.SPRAY_WATER_WARNING, null);
                // 在执行返回充电任务前给电机上锁--解决缺液状态无法返回充电的BUG
                iRobotMotorService.motorControl(true);
                log.info("缺液时的返回充电=============================================");
                rosWebService.gotoCharging();
                flag7 = false;
                if (sprayLiquidMessage) {
                    rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.LOW_LIQUID.getType());
                    sprayLiquidMessage = false;
                }
            }
            Thread.sleep(10000);
        } else {
            flag7 = true;
            // 如果不是缺液状态，将短信发送条件重置
            RobotStatusScheduler.sprayLiquidMessage = true;
        }
    }

    /**
     * 当水位满了之后，发出告警提示
     * 目前提供声音告警
     * <p>
     * 防止重复发送语音告警，这里做了一个简单的锁处理
     * 从播放语音开始，3秒后才可以继续播放下一段语音
     */
    private void handleOverSprayLiquidWarning() {
        if (!RobotType.U1.getType().equals(RobotBaseInfoConstant.type) && !RobotType.U3.getType().equals(RobotBaseInfoConstant.type)) {
            return;
        }
        if (iRobotStatusService.checkSprayLiquidOverflow()) {
            if (iRobotStatusService.checkTaskStatus()) {
//                log.info("机器人正在任务中，无需播报高液位告警");
                return;
            }
            if (iRobotStatusService.isDock()) {
//                log.info("机器人正在充电中，无需播报高液位告警");
                return;
            }
            // 当不再触发高液位时，重置条件（相当于一次高液位的情况只发送5次语音提醒）
            Integer i = 5;
            if (ControlStatusConstants.isFullSprayWiquid > i) {
                return;
            }
            if (ControlStatusConstants.isFullSprayWiquid <= i) {
                log.info("液位高了，告警语音");
                rosWebService.sendVoicePrompt(SPRAY_OVER_WATER_WARNING, null);
                ControlStatusConstants.isFullSprayWiquid++;
            }
            if (ControlStatusConstants.isFullSprayWiquid.equals(i)) {
                log.info("液位高了，发送消息");
                rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.FULL_LIQUID.getType());
            }
        } else {
            //当不处于高液位时，重置控制条件
            ControlStatusConstants.isFullSprayWiquid = 1;
        }
    }

    /**
     * 液位灯光检测
     */
    private void handleLiquidLightCheck() {
        if (iRobotStatusService.checkSprayLiquidOverflow()) {
            //发送满液的灯光
            iRobotStatusService.publishLiquidLightTopic(LiquidLightDefine.FULL_LIQUID.getValue());
            liquidTopicCtrl = true;
        } else if (iRobotStatusService.isSprayLiquidLevelWarning()) {
            //发布缺液的灯光
            iRobotStatusService.publishLiquidLightTopic(LiquidLightDefine.LACK_LIQUID.getValue());
            liquidTopicCtrl = true;
        } else {
            // 正常液位状态 - 发布有液的灯光
            // 移除liquidTopicCtrl的判断，确保正常状态下也能持续更新液位灯
            if (liquidTopicCtrl == true) {
                iRobotStatusService.publishLiquidLightTopic(LiquidLightDefine.HAVE_LIQUID.getValue());
                liquidTopicCtrl = false;
            }
        }
    }

    /**
     * 急停开关检测
     *
     * @throws Exception e
     */
    private void handleEmergencyCheck() throws Exception {
        //如果急停被按下
        if (iRobotMotorService.getEmergencyState()) {
            //发布急停被按下点阵表情话题---急停被按下
            rosWebService.publishUtil(ExpressionType.EMERGENCY_STOP.getValue());
            // 暂停任务
            if (operation2) {
                rosWebService.taskControl1("", TaskOperationType.STOP.getOperation().toString());
                // 如果机器人处于对接充电桩中
                Object autoChargingStatus = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
                if (autoChargingStatus != null && autoChargingStatus.toString().equals(AutoChargingStatus.DOING.getValue().toString())) {
                    rosWebService.taskControl1("", TaskOperationType.CANCEL.getOperation().toString());
                }
                operation2 = false;
            }
            boolean idle = rosWebService.isIdle();
            if (idle) {
                rosWebService.sendVoicePrompt(SceneType.EMERGENCY_ERROR_IDLE, null);
            } else {
                rosWebService.sendVoicePrompt(SceneType.EMERGENCY_ERROR_NOT_IDLE, null);
            }
            Thread.sleep(10000);
        } else {
            if (!operation2) {
                // 恢复工作
                if (!iRobotStatusService.getMotorLock()) {
                    log.info("充电状态:{}", iRobotStatusService.isDock());
                    log.info("机器人和充电桩接触状态:{}", iRobotStatusService.getSystemChargeState());
                    if (iRobotStatusService.isDock() || iRobotStatusService.getSystemChargeState()) {
                        String currentId = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                        if (StringUtils.isNotBlank(currentId)) {
                            rosWebService.taskControl1("", TaskOperationType.CANCEL.getOperation().toString());
                        }
                        iRobotMotorService.motorControl(true);
                    } else {
                        IS_MOTOR_LOCK_CONTINUE_TASK = false;
                        rosWebService.taskControl1("", TaskOperationType.CONTINUE.getOperation().toString());
                    }
                }
                operation2 = true;
            }
        }
    }

    /**
     * 消毒按键检测
     */
    private void handleDoctorKeyCheck() {
        String doctorKeyStatus = redisTemplate.opsForValue().get(TOPIC + "::" + TopicConstants.DOCTOR_KEY_STATUS);
        if (StrUtil.isNotEmpty(doctorKeyStatus)) {
            _Bool bool = JSON.parseObject(doctorKeyStatus, _Bool.class);
            if (Objects.requireNonNull(bool).data) {
                if (operation3) {
                    log.info("准备开始执行消毒任务......");
                    String defaultTaskId = iRobotTaskService.getDefaultTaskId();
                    log.info("默认任务为ID:{}", defaultTaskId);
                    if (StringUtils.isNotBlank(defaultTaskId)) {
                        rosWebService.startDisinfectService(defaultTaskId);
                    } else {
                        if (iRobotTaskService.isNeedLineTask()) {
                            rosWebService.sendVoicePrompt(SceneType.NOT_DEFAULT_TASK_LINE, null);
                        } else {
                            rosWebService.sendVoicePrompt(SceneType.NOT_EXEC_TASK_LINE, null);
                        }
                    }
                    operation3 = false;
                }
            } else {
                if (!operation3) {
                    operation3 = true;
                }
            }
        }
    }

    /**
     * 定位检测
     */
    private void handlePositionCheck() throws Exception {
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, POSITION_STATUS);
        if (ObjectUtil.isNull(o) || o.toString().equals(FAIL)) {
            //发布迷路点阵表情话题---迷路
            rosWebService.publishUtil(ExpressionType.LOST_WAY.getValue());
            rosWebService.sendVoicePrompt(SceneType.DEVIATION_ROUTE, null);
            Thread.sleep(10000);
        }
    }

    /**
     * 防撞条检测
     */
    private void handleCollisionCheck() throws Exception {
        // 防撞条是否触发
        boolean collision = rosWebService.isCollision();
        // 如果没触发防撞条，将短信通知条件重置
        if (!collision) {
            RobotStatusScheduler.collisionMessage = true;
        }
        // 是否在自动充电
        Object hasAutoCharging = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
        if (null != hasAutoCharging &&
                Integer.valueOf(hasAutoCharging.toString()).equals(AutoChargingStatus.SUCCESS.getValue())) {
            return;
        }

        if (collision) {
            count++;
            //发出一次语音提醒
            rosWebService.sendVoicePrompt(SceneType.BUMP_INTO_OBJECTS, null);
            if (!flag1) {
                //关闭所有消毒设备
                rosWebService.closeAllDisinfect();
            }
            flag1 = true;
            // 如果大于10s,则取消任务
            if (count > 1) {
                rosWebService.taskControl1("", "4");
                rosWebService.sendVoicePrompt(SceneType.CANCEL_WORK, null);
                if (collisionMessage) {
                    sendMessageUtil.sendShortMessage("系统自检防撞条触发");
                    rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.COLLISION_STRIP_TRIGGER.getType());
                    collisionMessage = false;
                }
                flag1 = false;
            }
            Thread.sleep(10000);
        } else {
            count = 0;
            if (flag1) {
                // 恢复任务，有风险存在检测不到的情况
                log.info("防撞条松开,继续工作......");
                rosWebService.taskControl1("", "3");
                rosWebService.sendVoicePrompt(SceneType.CONTINUE_WORK, null);
                flag1 = false;
            }
        }
    }

    /**
     * 系统自检状态检测
     */
    private void handleSystemCheck() throws Exception {
//        String systemCheckStr = redisTemplate.opsForValue().get(TOPIC + "::" + SYSTEM_CHECK);
        String systemCheckStr = RedisUtil.getTopicValue(SYSTEM_CHECK);
        if (StrUtil.isBlank(systemCheckStr)) {
            return;
        }
        boolean b = rosWebService.systemCheck(systemCheckStr);
        if (b) {
            if (flag5) {
                log.info("系统自检通过");
                systemStartSuccess = true;
                if (count1 == 0) {
                    rosWebService.sendVoicePrompt(SceneType.SYS_SUCCESS, null);
                    count1++;
                    if (iRobotStatusService.isSprayLiquidLevelWarning()) {
                        rosWebService.sendVoicePrompt(SceneType.SPRAY_WATER_WARNING, null);
                    }
                }
                rosWebService.sendRingLightPrompt(RingLightDefine.IDLE_STATE);
                flag5 = false;
                // 如果系统自检通过，则重置短信通知条件
                RobotStatusScheduler.systemCheckMessage = true;
            }
        } else {
            log.error("系统自检不通过");
            rosWebService.sendVoicePrompt(SceneType.SYS_FAIL, null);
            rosWebService.sendRingLightPrompt(RingLightDefine.SYS_FAIL);
            flag5 = true;
            //机器人故障通知发送的控制条件
            if (RobotStatusScheduler.systemCheckMessage) {
                rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.SYSTEM_CHECK_FAILURE.getType());
                systemCheckMessage = false;
            }
            // 休眠10s
            Thread.sleep(10000);
        }
    }

    /**
     * 地图检测
     */
    private void mapCheck() throws Exception {
        // 系统启动成功 检测地图节点是否启动
        boolean res = rosWebService.mapCheck();
        if (res) {
            log.info("地图检测通过");
        } else {
            // 进入智能建图
            log.warn("地图检测不通过");
            rosWebService.sendVoicePrompt(SceneType.START_MAP, null);
            Thread.sleep(10000);
        }
    }

    /**
     * 语音警告检测
     */
    private void handleVoiceWarnCheck() throws Exception {
        //语音警告开关
        boolean voiceWarning = iRobotStatusService.isOpenVoiceWarning();
        //活物检测开关
        boolean livingThingsUlray = iRobotStatusService.isLivingThingsUltraviolet();
        //是否发现人
        boolean findPeople = iRobotStatusService.isHumanDetectionStatus();
        //是否开紫外
        boolean ulrayStatus = iRobotStatusService.isUltravioletStatus();
        // 是否开启脉冲
        boolean pulseStatus = iRobotStatusService.isPulseStatus();

        boolean shieldingStatus = iRobotStatusService.isShieldingStatus();

        //是否开启了喷雾
        boolean sprayStatus = iRobotStatusService.isSprayStatus();
        boolean res1 = voiceWarning && livingThingsUlray && findPeople && ulrayStatus;
        boolean res2 = voiceWarning && livingThingsUlray && findPeople && sprayStatus;
        CHECK_LIVING_OPEN = iRobotStatusService.isLivingThingsUltraviolet() && iRobotStatusService.isHumanDetectionStatus();
        if (CHECK_LIVING_OPEN && ulrayStatus && iRobotTaskService.isDisinfectCurrentTaskType()) {
            log.warn("活物检测开启，并发现了人，并且紫外为开启，则关闭");
            rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.ULRAY_CTRL);
            checkUltraviolet = true;
        }

        if (CHECK_LIVING_OPEN && pulseStatus && iRobotTaskService.isDisinfectCurrentTaskType()) {
            log.warn("活物检测开启，并发现了人，关闭脉冲，则关闭");
            rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.PULSE_CTRL);
            checkPulse = true;
        }
        if (CHECK_LIVING_OPEN && shieldingStatus && !ulrayStatus && !pulseStatus
                && !sprayStatus && iRobotTaskService.isDisinfectCurrentTaskType()) {
//            rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.SHIELDING_CTRL);
            log.warn("活物检测开启，并发现了人，升降杆，则关闭");
            checkShielding = true;
        }
        if (false && CHECK_LIVING_OPEN && sprayStatus && iRobotTaskService.isDisinfectCurrentTaskType()) {
            log.warn("活物检测开启，并发现了人，并且喷雾为开启，则关闭");
            rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.SPRAY_CTRL);
            checkSpray = true;
        }
        // 活物检测状态开启，未检测到活物，且任务为正在执行的消毒任务
        Boolean openDevPreRequisites = livingThingsUlray && !findPeople && iRobotTaskService.isDisinfectCurrentTaskType();
        if (openDevPreRequisites && ControlStatusConstants.DEV_TASK_NEED_STATUS_ULTRAVIOLET) {
            log.warn("活物检测开启，没发现人，沿途紫外打开，但实际关闭，则打开");
            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.FAN_CTRL);
            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.SHIELDING_CTRL);
            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.ULRAY_CTRL);
        }
        if (openDevPreRequisites && ControlStatusConstants.DEV_TASK_NEED_STATUS_PULSE) {
            log.warn("活物检测开启，没发现人，沿途脉冲打开，但实际关闭，则打开");
            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.SHIELDING_CTRL);
            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.PULSE_CTRL);
        }
        if (openDevPreRequisites && !sprayStatus && ControlStatusConstants.DEV_TASK_NEED_STATUS_SPRAY) {
            log.warn("活物检测开启，没发现人，沿途喷雾打开，但实际关闭，则打开");
            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.SPRAY_CTRL);
        }
        if (res1 || res2) {
            if (flag3) {
                //消毒过程中
                rosWebService.sendVoicePrompt(SceneType.DISINFECT_WARNING, null);
                // 防止语音重复发送
                flag3 = !flag3;
            }
        } else {
            flag3 = !flag3;
        }
        Thread.sleep(900);
    }

    /**
     * 机器人电机检测
     */
    private void motorCheck() {
        String motorCheckStr = redisTemplate.opsForValue().get(TOPIC + "::" + MOTOR_DATA);
        if (StrUtil.isBlank(motorCheckStr)) {
            return;
        }
        boolean flag1 = false;
        boolean flag2 = false;
        boolean flag3 = false;
        boolean flag4 = false;
        boolean flag5 = false;
        boolean flag6 = false;
        _MotorData motorData = JSONObject.parseObject(motorCheckStr, _MotorData.class);
        if (0 != motorData.left_alarm_code) {
            MotorAlarmCode enumObjByKey = rosWebService.getEnumObjByKey(motorData.left_alarm_code);
            log.error("电机左侧故障码：" + motorData.left_alarm_code + ";原因：" + enumObjByKey.getValue());
            if (motorCheckMessage) {
                sendMessageUtil.sendShortMessage("电机左侧出现故障，故障码原因：" + enumObjByKey.getValue());
            }
            flag1 = false;
        } else {
            flag1 = true;
        }
        if (0 != motorData.right_alarm_code) {
            MotorAlarmCode enumObjByKey = rosWebService.getEnumObjByKey(motorData.right_alarm_code);
            log.error("电机右侧故障码：" + motorData.right_alarm_code + ";原因：" + enumObjByKey.getValue());
            if (motorCheckMessage) {
                sendMessageUtil.sendShortMessage("电机右侧出现故障，故障码原因：" + enumObjByKey.getValue());
            }
            flag2 = false;
        } else {
            flag2 = true;
        }
        if (motorData.left_temperature != 0 && motorData.left_temperature > 75) {
            log.error("电机左侧温度过高：" + motorData.left_temperature);
            if (motorCheckMessage) {
                sendMessageUtil.sendShortMessage("电机左侧温度过高，温度为：" + motorData.left_temperature);
            }
            flag3 = false;
        } else {
            flag3 = true;
        }
        if (motorData.right_temperature != 0 && motorData.right_temperature > 75) {
            log.error("电机右侧温度过高：" + motorData.right_temperature);
            if (motorCheckMessage) {
                sendMessageUtil.sendShortMessage("电机右侧温度过高，温度为：" + motorData.right_temperature);
            }
            flag4 = false;
        } else {
            flag4 = true;
        }
        if (motorData.left_load_rate != 0 && motorData.left_load_rate > 950) {
            log.error("电机左侧负载率过高：" + motorData.left_load_rate);
            if (motorCheckMessage) {
                sendMessageUtil.sendShortMessage("电机左侧负载率过高，负载为：" + motorData.left_load_rate / 1000);
            }
            flag5 = false;
        } else {
            flag5 = true;
        }
        if (motorData.right_load_rate != 0 && motorData.right_load_rate > 950) {
            log.error("电机右侧负载率过高：" + motorData.right_load_rate);
            if (motorCheckMessage) {
                sendMessageUtil.sendShortMessage("电机右侧负载率过高，负载为：" + motorData.right_load_rate / 1000);
            }
            flag6 = false;
        } else {
            flag6 = true;
        }
        // 如果检测到没有故障，将短信发送条件重置
        if (flag1 && flag2 && flag3 && flag4 && flag5 && flag6) {
            RobotStatusScheduler.motorCheckMessage = true;
        } else {
            //发送语音出故障
            rosWebService.sendVoicePrompt(SceneType.SYS_FAIL, null);
            //环形灯
            rosWebService.sendRingLightPrompt(RingLightDefine.SYS_FAIL);
            //停止任务
            rosWebService.taskControl1("", "2");
            //取消任务
            rosWebService.taskControl1("", "4");
            //电机使能
            iRobotMotorService.motorControl(true);
            RobotStatusScheduler.motorCheckMessage = false;
        }
    }

    /**
     * 充电或满电状态检测---用于点阵表情
     *
     * @throws Exception
     */
    private void batteryCheck() {
        _BatteryState batteryState = iRobotStatusService.getBatteryState();
        if (ObjectUtil.isNull(batteryState)) {
            batteryState = new _BatteryState();
        }
//      当前电量
        float batteryEletricRemain = batteryState.percentage;
//      是否在充电
        boolean charging = iRobotStatusService.isDock();
//      是否满电
        boolean fullPower = batteryEletricRemain == BATTERY_POWER_EXPRESSION.floatValue();
//      如果是满电状态下,
        if (fullPower && fullPowerCtrl) {
//         点阵表情话题发布---满电
            rosWebService.publishUtil(ExpressionType.FULL_BATTERY.getValue());
            fullPowerCtrl = false;
            return;
        }
        if (!fullPower) {
            fullPowerCtrl = true;
        }
//      如果是在充电状态下，
        if (charging && charingCtrl) {
            ControlStatusConstants.EXPRESSION_PRIORITY.gotoCharing = false;
//         点阵表情话题发布---充电中
            rosWebService.publishUtil(ExpressionType.CHARGING_ING.getValue());
            charingCtrl = false;
        }
        if (!charging) {
            charingCtrl = true;
        }
    }

    /**
     * 低电量检测---用于点阵表情
     */
    private void lowBattery() {
        _BatteryState batteryState = iRobotStatusService.getBatteryState();
        if (null != batteryState) {
            float batteryEletricRemain = batteryState.percentage;
            if (batteryEletricRemain <= BATTERY_POWER_THRESHOLD_WARNING.floatValue()) {
                //发布低电量点阵表情话题---低电量
                rosWebService.publishUtil(ExpressionType.LOW_BATTERY.getValue());
            }
        }
    }

    /**
     * 空闲状态检测---用于点阵表情
     */
    private void isIdle() {
//        如果机器人处于空闲状态随机发送微笑1和微笑2
        Random random = new Random();
//        随机生成0或者1
        int i = random.nextInt(2);
        if (rosWebService.isIdle() || CREATE_MAP_STATUE) {
            //如果再充电中就不发送
            if (iRobotStatusService.isDock()) {
                return;
            }
            if (i == 0) {
                //发布微笑点阵表情话题---微笑1
                rosWebService.publishUtil(ExpressionType.SMILE_TWO.getValue());
            } else if (i == 1) {
                //发布微笑点阵表情话题---微笑2
                rosWebService.publishUtil(ExpressionType.SMILE_ONE.getValue());
            }
            ControlStatusConstants.EXPRESSION_PRIORITY.working = false;
        }
        // 如果处在建图状态，发送蓝色闪烁灯光
        if (CREATE_MAP_STATUE) {
            rosWebService.sendRingLightPrompt(RingLightDefine.MAPPING_STATE);
        }
    }

    /**
     * 防跌落检测
     */
    private void motorLockCheck() {
        Boolean isMotorLock = iRobotStatusService.getMotorLock();
        RobotTask currentTask = rosWebService.getCurrentTask();
        if (isMotorLock) {
            log.warn("防跌落触发！");
        }
        //如果当前有任务并且防跌落话题数据不为空
        if (ObjectUtil.isNotNull(currentTask)) {
            //获取急停状态
            boolean isStop = rosWebService.isStopping();
            //如果防跌落触发(true:电机锁定),增加控制条件，避免重复执行暂停任务操作
            if (isMotorLock && !ControlStatusConstants.MOTOR_LOCK_CONTROL) {
                log.warn("防跌落触发，任务暂停！");
                //暂停任务(由taskControl演变而来)
                IS_ROBOT_LINE_EXEC_STATUS_CANCEL = 1;
                rosWebService.stopTaskService();
                ThreadUtil.sleep(1000);
                //给电机使能，防止坡度下滑
                iRobotMotorService.motorControl(true);
                //更改防跌落控制条件
                ControlStatusConstants.MOTOR_LOCK_CONTROL = true;
                //如果防跌落触发，发送短信
                if (fallCtrl) {
                    log.info("机器人防跌落触发短信发送！");
                    sendMessageUtil.sendShortMessage("机器人防跌落触发");
                    fallCtrl = false;
                }
            }
            //如果防跌落触发，每检测10次发送一次语音（不使用线程睡眠，防止影响其他程序）；
            boolean flag = MOTOR_LOCK_COUNT % 10 == 0;
            if (isMotorLock && flag) {
                rosWebService.sendVoicePrompt(MOCK_LOCK_WARN, null);
                log.info("防跌落语音发送！");
            }
            MOTOR_LOCK_COUNT++;
            //如果防跌落解锁（false:电机解锁） 并且急停没被按下 并且防跌落控制条件为true
            if (!isMotorLock && !isStop && ControlStatusConstants.MOTOR_LOCK_CONTROL) {
                log.warn("防跌落解锁，继续执行任务");
                fallCtrl = true;
                //重置语音发送控制条件
                MOTOR_LOCK_COUNT = 10;
                if (IS_MOTOR_LOCK_CONTINUE_TASK) {
                    //给电机使能
                    iRobotMotorService.motorControl(true);
                    //继续任务
//                    rosWebService.taskControl1(currentTask.getUuId(), TaskOperationType.CONTINUE.getOperation().toString());
                } else {
                    log.info("防跌落解锁，急停已触发继续工作，无需再次触发");
                    IS_MOTOR_LOCK_CONTINUE_TASK = true;
                }
                //更改防跌落控制条件
                ControlStatusConstants.MOTOR_LOCK_CONTROL = false;
            }
        }
    }


    /**
     * 电池健康状态检测
     */
    private void batteryHealthCheck() {
        String batteryCheckStr = redisTemplate.opsForValue().get(TOPIC + "::" + WHOLE_BATTERY_STATE);
        _BatteryHealthState batteryState = JSONObject.parseObject(batteryCheckStr, _BatteryHealthState.class);
        //电池温度
        float[] temperature = batteryState.temperature_NTC;
        double num = 0;
        for (float x : temperature) {
            num = NumberUtil.add(num, x);
        }
        double f = num / temperature.length;
        // 如果是在充电状态下
        if (f >= TEMPERATURE_STOP_CHARING && iRobotStatusService.isDock()) {
            iRobotChargingService.autoDockControl(4);
            iRobotChargingService.autoDockControl(3);
            log.warn("电池温度过高，停止充电，当前温度为：" + f);
        }
        //充电次数
        Short cycleIndex = batteryState.cycle_index;
        DecimalFormat decFormat = new DecimalFormat("#%");
        Double cyclePercent = cycleIndex / 1600.00;
        //充电次数百分比
        RobotBaseInfoConstant.BATTERY_CYCLE_PERCENT = decFormat.format(cyclePercent);
        if (cycleIndex == 1600) {
            //发送短信提醒
            sendMessageUtil.sendShortMessage("电池充电次数已满，请及时更换");
        }
    }
}
